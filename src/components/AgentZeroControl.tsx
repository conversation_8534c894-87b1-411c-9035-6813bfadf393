import React, { useState, useEffect } from 'react';
import { Mic, MicOff } from 'lucide-react';

interface Agent {
  id: number;
  name: string;
  status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'OFFLINE';
  task?: string;
  performance: number;
}

const AgentZeroControl: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([
    {
      id: 1,
      name: 'Agent 1',
      status: 'ACTIVE',
      task: 'Analyzing data',
      performance: 75
    },
    {
      id: 2,
      name: 'Agent 2',
      status: 'THINKING',
      performance: 60
    },
    {
      id: 3,
      name: 'Agent 3',
      status: 'ERROR',
      performance: 45
    },
    {
      id: 4,
      name: 'Agent 4',
      status: 'OFFLINE',
      performance: 0
    }
  ]);

  const [command, setCommand] = useState('');
  const [isListening, setIsListening] = useState(false);

  // Simuleer dynamische performance updates
  useEffect(() => {
    const interval = setInterval(() => {
      setAgents(prevAgents => 
        prevAgents.map(agent => {
          if (agent.status === 'ACTIVE') {
            const newPerformance = Math.min(100, agent.performance + Math.random() * 5 - 2);
            return { ...agent, performance: Math.max(0, newPerformance) };
          }
          return agent;
        })
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'ACTIVE':
        return {
          bg: 'bg-yellow-500/20',
          border: 'border-yellow-500',
          text: 'text-yellow-400',
          glow: 'shadow-yellow-500/50',
          progress: 'bg-gradient-to-r from-yellow-600 to-orange-500'
        };
      case 'THINKING':
        return {
          bg: 'bg-blue-500/20',
          border: 'border-blue-500',
          text: 'text-blue-400',
          glow: 'shadow-blue-500/50',
          progress: 'bg-gradient-to-r from-blue-600 to-cyan-500'
        };
      case 'ERROR':
        return {
          bg: 'bg-red-500/20',
          border: 'border-red-500',
          text: 'text-red-400',
          glow: 'shadow-red-500/50',
          progress: 'bg-gradient-to-r from-red-600 to-orange-600'
        };
      default:
        return {
          bg: 'bg-gray-500/20',
          border: 'border-gray-600',
          text: 'text-gray-400',
          glow: 'shadow-gray-500/30',
          progress: 'bg-gradient-to-r from-gray-600 to-gray-500'
        };
    }
  };

  const handleMicToggle = () => {
    setIsListening(!isListening);
    // Hier zou je voice recognition implementeren
  };

  const handleCommandSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (command.trim()) {
      console.log('Command sent:', command);
      setCommand('');
    }
  };

  return (
    <div className="min-h-screen bg-black text-white font-mono">
      {/* Header */}
      <div className="text-center py-8 px-4">
        <h1 className="text-2xl md:text-3xl font-bold tracking-wider text-white">
          AGENT ZERO CONTROL
        </h1>
        <div className="w-full h-px bg-gradient-to-r from-transparent via-cyan-500 to-transparent mt-4"></div>
      </div>

      {/* Agent Cards */}
      <div className="px-4 space-y-4 pb-24">
        {agents.map((agent) => {
          const colors = getStatusColor(agent.status);
          
          return (
            <div
              key={agent.id}
              className={`
                relative p-4 rounded-lg border-2 backdrop-blur-sm transition-all duration-300
                ${colors.bg} ${colors.border} hover:shadow-lg hover:${colors.glow}
              `}
            >
              {/* Agent Header */}
              <div className="flex justify-between items-center mb-3">
                <h3 className={`text-lg font-bold ${colors.text}`}>
                  {agent.name}
                </h3>
                <div className={`
                  px-3 py-1 rounded-full text-xs font-semibold uppercase tracking-wider
                  ${colors.bg} ${colors.text} border ${colors.border}
                `}>
                  {agent.status}
                </div>
              </div>

              {/* Task Info */}
              {agent.task && (
                <div className="mb-3">
                  <p className="text-gray-300 text-sm">
                    <span className="text-cyan-400">Task:</span> {agent.task}
                  </p>
                </div>
              )}

              {/* Performance Bar */}
              <div className="mb-2">
                <div className="flex justify-between items-center mb-1">
                  <span className="text-xs text-gray-400">Performance</span>
                  <span className={`text-xs font-mono ${colors.text}`}>
                    {Math.round(agent.performance)}%
                  </span>
                </div>
                <div className="w-full bg-gray-800 rounded-full h-2 overflow-hidden">
                  <div
                    className={`h-full transition-all duration-1000 ${colors.progress}`}
                    style={{ width: `${agent.performance}%` }}
                  />
                </div>
              </div>

              {/* Status Indicator */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`
                    w-2 h-2 rounded-full ${colors.text.replace('text-', 'bg-')}
                    ${agent.status === 'ACTIVE' ? 'animate-pulse' : ''}
                  `} />
                  <span className="text-xs text-gray-400">
                    {agent.status === 'ACTIVE' ? 'Processing...' :
                     agent.status === 'THINKING' ? 'Analyzing...' :
                     agent.status === 'ERROR' ? 'Error detected' : 'Offline'}
                  </span>
                </div>

                {agent.status === 'ACTIVE' && (
                  <div className="flex space-x-1">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-1 h-4 ${colors.progress} rounded-full animate-pulse`}
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                    ))}
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Command Interface */}
      <div className="fixed bottom-0 left-0 right-0 bg-black/90 backdrop-blur-sm border-t border-cyan-500/30 p-4">
        <form onSubmit={handleCommandSubmit} className="max-w-4xl mx-auto">
          <div className="flex items-center space-x-3">
            {/* Voice Toggle */}
            <button
              type="button"
              onClick={handleMicToggle}
              className={`
                p-3 rounded-full border-2 transition-all duration-300
                ${isListening
                  ? 'bg-red-500/20 border-red-500 text-red-400 shadow-lg shadow-red-500/50'
                  : 'bg-cyan-500/20 border-cyan-500 text-cyan-400 hover:shadow-lg hover:shadow-cyan-500/50'
                }
              `}
            >
              {isListening ? <MicOff size={20} /> : <Mic size={20} />}
            </button>

            {/* Command Input */}
            <div className="flex-1 relative">
              <input
                type="text"
                value={command}
                onChange={(e) => setCommand(e.target.value)}
                placeholder="Enter command for agents..."
                className="
                  w-full px-4 py-3 bg-gray-900/50 border-2 border-gray-600 rounded-lg
                  text-white placeholder-gray-400 font-mono
                  focus:border-cyan-500 focus:outline-none focus:shadow-lg focus:shadow-cyan-500/30
                  transition-all duration-300
                "
              />
              {isListening && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div className="flex space-x-1">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className="w-1 h-4 bg-red-500 rounded-full animate-pulse"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Send Button */}
            <button
              type="submit"
              disabled={!command.trim()}
              className="
                px-6 py-3 bg-gradient-to-r from-cyan-600 to-blue-600
                text-white font-semibold rounded-lg border-2 border-cyan-500
                hover:shadow-lg hover:shadow-cyan-500/50
                disabled:opacity-50 disabled:cursor-not-allowed
                transition-all duration-300
              "
            >
              SEND
            </button>
          </div>
        </form>

        {/* Status Bar */}
        <div className="mt-3 flex justify-center">
          <div className="flex items-center space-x-6 text-xs text-gray-400">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
              <span>Active: {agents.filter(a => a.status === 'ACTIVE').length}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full" />
              <span>Thinking: {agents.filter(a => a.status === 'THINKING').length}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-red-500 rounded-full" />
              <span>Errors: {agents.filter(a => a.status === 'ERROR').length}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-gray-500 rounded-full" />
              <span>Offline: {agents.filter(a => a.status === 'OFFLINE').length}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AgentZeroControl;
