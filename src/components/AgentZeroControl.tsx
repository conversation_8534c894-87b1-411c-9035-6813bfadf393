import React, { useState, useEffect } from 'react';
import { Mic, MicOff } from 'lucide-react';

interface Agent {
  id: number;
  name: string;
  status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'OFFLINE';
  task?: string;
  performance: number;
}

const AgentZeroControl: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([
    {
      id: 1,
      name: 'Agent 1',
      status: 'ACTIVE',
      task: 'Analyzing data',
      performance: 75
    },
    {
      id: 2,
      name: 'Agent 2',
      status: 'THINKING',
      performance: 60
    },
    {
      id: 3,
      name: 'Agent 3',
      status: 'ERROR',
      performance: 45
    },
    {
      id: 4,
      name: 'Agent 4',
      status: 'OFFLINE',
      performance: 0
    }
  ]);

  const [command, setCommand] = useState('');
  const [isListening, setIsListening] = useState(false);

  // Simuleer dynamische performance updates
  useEffect(() => {
    const interval = setInterval(() => {
      setAgents(prevAgents => 
        prevAgents.map(agent => {
          if (agent.status === 'ACTIVE') {
            const newPerformance = Math.min(100, agent.performance + Math.random() * 5 - 2);
            return { ...agent, performance: Math.max(0, newPerformance) };
          }
          return agent;
        })
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'ACTIVE':
        return {
          bg: 'bg-yellow-500/20',
          border: 'border-yellow-500',
          text: 'text-yellow-400',
          glow: 'shadow-yellow-500/50',
          progress: 'bg-gradient-to-r from-yellow-600 to-orange-500'
        };
      case 'THINKING':
        return {
          bg: 'bg-blue-500/20',
          border: 'border-blue-500',
          text: 'text-blue-400',
          glow: 'shadow-blue-500/50',
          progress: 'bg-gradient-to-r from-blue-600 to-cyan-500'
        };
      case 'ERROR':
        return {
          bg: 'bg-red-500/20',
          border: 'border-red-500',
          text: 'text-red-400',
          glow: 'shadow-red-500/50',
          progress: 'bg-gradient-to-r from-red-600 to-orange-600'
        };
      default:
        return {
          bg: 'bg-gray-500/20',
          border: 'border-gray-600',
          text: 'text-gray-400',
          glow: 'shadow-gray-500/30',
          progress: 'bg-gradient-to-r from-gray-600 to-gray-500'
        };
    }
  };

  const handleMicToggle = () => {
    setIsListening(!isListening);
    // Hier zou je voice recognition implementeren
  };

  const handleCommandSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (command.trim()) {
      console.log('Command sent:', command);
      setCommand('');
    }
  };

  return (
    <div className="min-h-screen bg-black text-white font-mono">
      {/* Header */}
      <div className="text-center py-8 px-4">
        <h1 className="text-2xl md:text-3xl font-bold tracking-wider text-white">
          AGENT ZERO CONTROL
        </h1>
        <div className="w-full h-px bg-gradient-to-r from-transparent via-cyan-500 to-transparent mt-4"></div>
      </div>

      {/* Agent Cards */}
      <div className="px-4 space-y-4 pb-24">
        {agents.map((agent) => {
          const colors = getStatusColor(agent.status);
          
          return (
            <div
              key={agent.id}
              className={`
                relative p-4 rounded-lg border-2 backdrop-blur-sm
                ${colors.bg} ${colors.border}
