{"ast": null, "code": "/**\n * lucide-react v0.0.1 - ISC\n */\n\nimport createLucideIcon from '../createLucideIcon.mjs';\nconst ScissorsSquareDashedBottom = createLucideIcon(\"ScissorsSquareDashedBottom\", [[\"path\", {\n  d: \"M4 22a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2\",\n  key: \"1vzg26\"\n}], [\"path\", {\n  d: \"M10 22H8\",\n  key: \"euku7a\"\n}], [\"path\", {\n  d: \"M16 22h-2\",\n  key: \"18d249\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"8\",\n  r: \"2\",\n  key: \"14cg06\"\n}], [\"path\", {\n  d: \"M9.414 9.414 12 12\",\n  key: \"qz4lzr\"\n}], [\"path\", {\n  d: \"M14.8 14.8 18 18\",\n  key: \"11flf1\"\n}], [\"circle\", {\n  cx: \"8\",\n  cy: \"16\",\n  r: \"2\",\n  key: \"1acxsx\"\n}], [\"path\", {\n  d: \"m18 6-8.586 8.586\",\n  key: \"11kzk1\"\n}]]);\nexport { ScissorsSquareDashedBottom as default };", "map": {"version": 3, "names": ["ScissorsSquareDashedBottom", "createLucideIcon", "d", "key", "cx", "cy", "r"], "sources": ["/Users/<USER>/Desktop/agent-zero/node_modules/lucide-react/src/icons/scissors-square-dashed-bottom.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name ScissorsSquareDashedBottom\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAyMmEyIDIgMCAwIDEtMi0yVjRhMiAyIDAgMCAxIDItMmgxNmEyIDIgMCAwIDEgMiAydjE2YTIgMiAwIDAgMS0yIDIiIC8+CiAgPHBhdGggZD0iTTEwIDIySDgiIC8+CiAgPHBhdGggZD0iTTE2IDIyaC0yIiAvPgogIDxjaXJjbGUgY3g9IjgiIGN5PSI4IiByPSIyIiAvPgogIDxwYXRoIGQ9Ik05LjQxNCA5LjQxNCAxMiAxMiIgLz4KICA8cGF0aCBkPSJNMTQuOCAxNC44IDE4IDE4IiAvPgogIDxjaXJjbGUgY3g9IjgiIGN5PSIxNiIgcj0iMiIgLz4KICA8cGF0aCBkPSJtMTggNi04LjU4NiA4LjU4NiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/scissors-square-dashed-bottom\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ScissorsSquareDashedBottom = createLucideIcon(\n  'ScissorsSquareDashedBottom',\n  [\n    [\n      'path',\n      {\n        d: 'M4 22a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v16a2 2 0 0 1-2 2',\n        key: '1vzg26',\n      },\n    ],\n    ['path', { d: 'M10 22H8', key: 'euku7a' }],\n    ['path', { d: 'M16 22h-2', key: '18d249' }],\n    ['circle', { cx: '8', cy: '8', r: '2', key: '14cg06' }],\n    ['path', { d: 'M9.414 9.414 12 12', key: 'qz4lzr' }],\n    ['path', { d: 'M14.8 14.8 18 18', key: '11flf1' }],\n    ['circle', { cx: '8', cy: '16', r: '2', key: '1acxsx' }],\n    ['path', { d: 'm18 6-8.586 8.586', key: '11kzk1' }],\n  ],\n);\n\nexport default ScissorsSquareDashedBottom;\n"], "mappings": ";;;;;AAaA,MAAMA,0BAA6B,GAAAC,gBAAA,CACjC,8BACA,CACE,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAKC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACtD,CAAC,MAAQ;EAAED,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,QAAU;EAAEC,EAAI;EAAKC,EAAI;EAAMC,CAAG;EAAKH,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAED,CAAA,EAAG,mBAAqB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAEtD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}