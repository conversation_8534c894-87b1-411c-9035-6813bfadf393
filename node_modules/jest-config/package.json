{"name": "jest-config", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-config"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "peerDependencies": {"ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"ts-node": {"optional": true}}, "dependencies": {"@babel/core": "^7.8.0", "@jest/test-sequencer": "^27.5.1", "@jest/types": "^27.5.1", "babel-jest": "^27.5.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.1", "graceful-fs": "^4.2.9", "jest-circus": "^27.5.1", "jest-environment-jsdom": "^27.5.1", "jest-environment-node": "^27.5.1", "jest-get-type": "^27.5.1", "jest-jasmine2": "^27.5.1", "jest-regex-util": "^27.5.1", "jest-resolve": "^27.5.1", "jest-runner": "^27.5.1", "jest-util": "^27.5.1", "jest-validate": "^27.5.1", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^27.5.1", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "devDependencies": {"@types/glob": "^7.1.1", "@types/graceful-fs": "^4.1.3", "@types/micromatch": "^4.0.1", "semver": "^7.3.5", "ts-node": "^9.0.0", "typescript": "^4.0.3"}, "engines": {"node": "^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}