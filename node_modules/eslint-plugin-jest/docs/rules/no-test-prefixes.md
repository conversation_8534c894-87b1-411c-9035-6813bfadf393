# Use `.only` and `.skip` over `f` and `x` (`no-test-prefixes`)

Je<PERSON> allows you to choose how you want to define focused and skipped tests, with
multiple permutations for each:

- **only & skip:** `it.only`, `test.only`, `describe.only`, `it.skip`,
  `test.skip`, `describe.skip`.
- **'f' & 'x':** `fit`, `fdescribe`, `xit`, `xtest`, `xdescribe`.

This rule enforces usages from the **only & skip** list.

## Rule details

This rule triggers a warning if you use one of the keywords from the **'f' &
'x'** list to focus/skip a test.

```js
/*eslint jest/no-test-prefixes: "error"*/

it.only('foo'); // valid
test.only('foo'); // valid
describe.only('foo'); // valid
it.skip('foo'); // valid
test.skip('foo'); // valid
describe.skip('foo'); // valid

fit('foo'); // invalid
fdescribe('foo'); // invalid
xit('foo'); // invalid
xtest('foo'); // invalid
xdescribe('foo'); // invalid
```
