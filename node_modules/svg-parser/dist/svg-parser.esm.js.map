{"version": 3, "file": "svg-parser.esm.js", "sources": ["../node_modules/locate-character/dist/locate-character.es.js", "../src/index.js"], "sourcesContent": ["function getLocator(source, options) {\n    if (options === void 0) { options = {}; }\n    var offsetLine = options.offsetLine || 0;\n    var offsetColumn = options.offsetColumn || 0;\n    var originalLines = source.split('\\n');\n    var start = 0;\n    var lineRanges = originalLines.map(function (line, i) {\n        var end = start + line.length + 1;\n        var range = { start: start, end: end, line: i };\n        start = end;\n        return range;\n    });\n    var i = 0;\n    function rangeContains(range, index) {\n        return range.start <= index && index < range.end;\n    }\n    function getLocation(range, index) {\n        return { line: offsetLine + range.line, column: offsetColumn + index - range.start, character: index };\n    }\n    function locate(search, startIndex) {\n        if (typeof search === 'string') {\n            search = source.indexOf(search, startIndex || 0);\n        }\n        var range = lineRanges[i];\n        var d = search >= range.end ? 1 : -1;\n        while (range) {\n            if (rangeContains(range, search))\n                return getLocation(range, search);\n            i += d;\n            range = lineRanges[i];\n        }\n    }\n    ;\n    return locate;\n}\nfunction locate(source, search, options) {\n    if (typeof options === 'number') {\n        throw new Error('locate takes a { startIndex, offsetLine, offsetColumn } object as the third argument');\n    }\n    return getLocator(source, options)(search, options && options.startIndex);\n}\n\nexport { getLocator, locate };", "import { locate } from 'locate-character';\n\nconst validNameCharacters = /[a-zA-Z0-9:_-]/;\nconst whitespace = /[\\s\\t\\r\\n]/;\nconst quotemark = /['\"]/;\n\nfunction repeat(str, i) {\n\tlet result = '';\n\twhile (i--) result += str;\n\treturn result;\n}\n\nexport function parse(source) {\n\tlet header = '';\n\tlet stack = [];\n\n\tlet state = metadata;\n\tlet currentElement = null;\n\tlet root = null;\n\n\tfunction error(message) {\n\t\tconst { line, column } = locate(source, i);\n\t\tconst before = source.slice(0, i);\n\t\tconst beforeLine = /(^|\\n).*$/.exec(before)[0].replace(/\\t/g, '  ');\n\t\tconst after = source.slice(i);\n\t\tconst afterLine = /.*(\\n|$)/.exec(after)[0];\n\n\t\tconst snippet = `${beforeLine}${afterLine}\\n${repeat(' ', beforeLine.length)}^`;\n\n\t\tthrow new Error(\n\t\t\t`${message} (${line}:${column}). If this is valid SVG, it's probably a bug in svg-parser. Please raise an issue at https://github.com/Rich-Harris/svg-parser/issues – thanks!\\n\\n${snippet}`\n\t\t);\n\t}\n\n\tfunction metadata() {\n\t\twhile ((i < source.length && source[i] !== '<') || !validNameCharacters.test(source[i + 1])) {\n\t\t\theader += source[i++];\n\t\t}\n\n\t\treturn neutral();\n\t}\n\n\tfunction neutral() {\n\t\tlet text = '';\n\t\twhile (i < source.length && source[i] !== '<') text += source[i++];\n\n\t\tif (/\\S/.test(text)) {\n\t\t\tcurrentElement.children.push({ type: 'text', value: text });\n\t\t}\n\n\t\tif (source[i] === '<') {\n\t\t\treturn tag;\n\t\t}\n\n\t\treturn neutral;\n\t}\n\n\tfunction tag() {\n\t\tconst char = source[i];\n\n\t\tif (char === '?') return neutral; // <?xml...\n\n\t\tif (char === '!') {\n\t\t\tif (source.slice(i + 1, i + 3) === '--') return comment;\n\t\t\tif (source.slice(i + 1, i + 8) === '[CDATA[') return cdata;\n\t\t\tif (/doctype/i.test(source.slice(i + 1, i + 8))) return neutral;\n\t\t}\n\n\t\tif (char === '/') return closingTag;\n\n\t\tconst tagName = getName();\n\n\t\tconst element = {\n\t\t\ttype: 'element',\n\t\t\ttagName,\n\t\t\tproperties: {},\n\t\t\tchildren: []\n\t\t};\n\n\t\tif (currentElement) {\n\t\t\tcurrentElement.children.push(element);\n\t\t} else {\n\t\t\troot = element;\n\t\t}\n\n\t\tlet attribute;\n\t\twhile (i < source.length && (attribute = getAttribute())) {\n\t\t\telement.properties[attribute.name] = attribute.value;\n\t\t}\n\n\t\tlet selfClosing = false;\n\n\t\tif (source[i] === '/') {\n\t\t\ti += 1;\n\t\t\tselfClosing = true;\n\t\t}\n\n\t\tif (source[i] !== '>') {\n\t\t\terror('Expected >');\n\t\t}\n\n\t\tif (!selfClosing) {\n\t\t\tcurrentElement = element;\n\t\t\tstack.push(element);\n\t\t}\n\n\t\treturn neutral;\n\t}\n\n\tfunction comment() {\n\t\tconst index = source.indexOf('-->', i);\n\t\tif (!~index) error('expected -->');\n\n\t\ti = index + 2;\n\t\treturn neutral;\n\t}\n\n\tfunction cdata() {\n\t\tconst index = source.indexOf(']]>', i);\n\t\tif (!~index) error('expected ]]>');\n\n\t\tcurrentElement.children.push(source.slice(i + 7, index));\n\n\t\ti = index + 2;\n\t\treturn neutral;\n\t}\n\n\tfunction closingTag() {\n\t\tconst tagName = getName();\n\n\t\tif (!tagName) error('Expected tag name');\n\n\t\tif (tagName !== currentElement.tagName) {\n\t\t\terror(`Expected closing tag </${tagName}> to match opening tag <${currentElement.tagName}>`);\n\t\t}\n\n\t\tallowSpaces();\n\n\t\tif (source[i] !== '>') {\n\t\t\terror('Expected >');\n\t\t}\n\n\t\tstack.pop();\n\t\tcurrentElement = stack[stack.length - 1];\n\n\t\treturn neutral;\n\t}\n\n\tfunction getName() {\n\t\tlet name = '';\n\t\twhile (i < source.length && validNameCharacters.test(source[i])) name += source[i++];\n\n\t\treturn name;\n\t}\n\n\tfunction getAttribute() {\n\t\tif (!whitespace.test(source[i])) return null;\n\t\tallowSpaces();\n\n\t\tconst name = getName();\n\t\tif (!name) return null;\n\n\t\tlet value = true;\n\n\t\tallowSpaces();\n\t\tif (source[i] === '=') {\n\t\t\ti += 1;\n\t\t\tallowSpaces();\n\n\t\t\tvalue = getAttributeValue();\n\t\t\tif (!isNaN(value) && value.trim() !== '') value = +value; // TODO whitelist numeric attributes?\n\t\t}\n\n\t\treturn { name, value };\n\t}\n\n\tfunction getAttributeValue() {\n\t\treturn quotemark.test(source[i]) ? getQuotedAttributeValue() : getUnquotedAttributeValue();\n\t}\n\n\tfunction getUnquotedAttributeValue() {\n\t\tlet value = '';\n\t\tdo {\n\t\t\tconst char = source[i];\n\t\t\tif (char === ' ' || char === '>' || char === '/') {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tvalue += char;\n\t\t\ti += 1;\n\t\t} while (i < source.length);\n\n\t\treturn value;\n\t}\n\n\tfunction getQuotedAttributeValue() {\n\t\tconst quotemark = source[i++];\n\n\t\tlet value = '';\n\t\tlet escaped = false;\n\n\t\twhile (i < source.length) {\n\t\t\tconst char = source[i++];\n\t\t\tif (char === quotemark && !escaped) {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tif (char === '\\\\' && !escaped) {\n\t\t\t\tescaped = true;\n\t\t\t}\n\n\t\t\tvalue += escaped ? `\\\\${char}` : char;\n\t\t\tescaped = false;\n\t\t}\n\t}\n\n\tfunction allowSpaces() {\n\t\twhile (i < source.length && whitespace.test(source[i])) i += 1;\n\t}\n\n\tlet i = metadata.length;\n\twhile (i < source.length) {\n\t\tif (!state) error('Unexpected character');\n\t\tstate = state();\n\t\ti += 1;\n\t}\n\n\tif (state !== neutral) {\n\t\terror('Unexpected end of input');\n\t}\n\n\tif (root.tagName === 'svg') root.metadata = header;\n\treturn {\n\t\ttype: 'root',\n\t\tchildren: [root]\n\t};\n}\n"], "names": ["const", "let"], "mappings": "AAAA,SAAS,UAAU,CAAC,MAAM,EAAE,OAAO,EAAE;AACrC,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,CAAC,CAAC;AAC7C,IAAI,IAAI,YAAY,GAAG,OAAO,CAAC,YAAY,IAAI,CAAC,CAAC;AACjD,IAAI,IAAI,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAC3C,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC;AAClB,IAAI,IAAI,UAAU,GAAG,aAAa,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE,CAAC,EAAE;AAC1D,QAAQ,IAAI,GAAG,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AAC1C,QAAQ,IAAI,KAAK,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;AACxD,QAAQ,KAAK,GAAG,GAAG,CAAC;AACpB,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,SAAS,aAAa,CAAC,KAAK,EAAE,KAAK,EAAE;AACzC,QAAQ,OAAO,KAAK,CAAC,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,CAAC;AACzD,KAAK;AACL,IAAI,SAAS,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;AACvC,QAAQ,OAAO,EAAE,IAAI,EAAE,UAAU,GAAG,KAAK,CAAC,IAAI,EAAE,MAAM,EAAE,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC/G,KAAK;AACL,IAAI,SAAS,MAAM,CAAC,MAAM,EAAE,UAAU,EAAE;AACxC,QAAQ,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AACxC,YAAY,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC,CAAC,CAAC;AAC7D,SAAS;AACT,QAAQ,IAAI,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,QAAQ,IAAI,CAAC,GAAG,MAAM,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC7C,QAAQ,OAAO,KAAK,EAAE;AACtB,YAAY,IAAI,aAAa,CAAC,KAAK,EAAE,MAAM,CAAC;AAC5C,gBAAgB,OAAO,WAAW,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;AAClD,YAAY,CAAC,IAAI,CAAC,CAAC;AACnB,YAAY,KAAK,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AAClC,SAAS;AACT,KAAK;AAEL,IAAI,OAAO,MAAM,CAAC;AAClB,CAAC;AACD,SAAS,MAAM,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE;AACzC,IAAI,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE;AACrC,QAAQ,MAAM,IAAI,KAAK,CAAC,sFAAsF,CAAC,CAAC;AAChH,KAAK;AACL,IAAI,OAAO,UAAU,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC;AAC9E;;ACtCAA,IAAM,mBAAmB,GAAG,gBAAgB,CAAC;AAC7CA,IAAM,UAAU,GAAG,YAAY,CAAC;AAChCA,IAAM,SAAS,GAAG,MAAM,CAAC;AACzB;AACA,SAAS,MAAM,CAAC,GAAG,EAAE,CAAC,EAAE;AACxB,CAACC,IAAI,MAAM,GAAG,EAAE,CAAC;AACjB,CAAC,OAAO,CAAC,EAAE,IAAE,MAAM,IAAI,GAAG,GAAC;AAC3B,CAAC,OAAO,MAAM,CAAC;AACf,CAAC;AACD;AACA,AAAO,SAAS,KAAK,CAAC,MAAM,EAAE;AAC9B,CAACA,IAAI,MAAM,GAAG,EAAE,CAAC;AACjB,CAACA,IAAI,KAAK,GAAG,EAAE,CAAC;AAChB;AACA,CAACA,IAAI,KAAK,GAAG,QAAQ,CAAC;AACtB,CAACA,IAAI,cAAc,GAAG,IAAI,CAAC;AAC3B,CAACA,IAAI,IAAI,GAAG,IAAI,CAAC;AACjB;AACA,CAAC,SAAS,KAAK,CAAC,OAAO,EAAE;AACzB,SAAwB,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC;EAAjC;EAAM,wBAA6B;AAC7C,EAAED,IAAM,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACpC,EAAEA,IAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AACtE,EAAEA,IAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAChC,EAAEA,IAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9C;AACA,EAAEA,IAAM,OAAO,GAAG,KAAG,aAAa,SAAS,WAAK,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,MAAM,EAAC,MAAG,CAAC;AAClF;AACA,EAAE,MAAM,IAAI,KAAK;AACjB,IAAM,OAAO,UAAK,IAAI,SAAI,MAAM,2JAAsJ;AACtL,GAAG,CAAC;AACJ,EAAE;AACF;AACA,CAAC,SAAS,QAAQ,GAAG;AACrB,EAAE,OAAO,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;AAC/F,GAAG,MAAM,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACzB,GAAG;AACH;AACA,EAAE,OAAO,OAAO,EAAE,CAAC;AACnB,EAAE;AACF;AACA,CAAC,SAAS,OAAO,GAAG;AACpB,EAAEC,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAE,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,GAAC;AACrE;AACA,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;AACvB,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;AAC/D,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,GAAG,OAAO,GAAG,CAAC;AACd,GAAG;AACH;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF;AACA,CAAC,SAAS,GAAG,GAAG;AAChB,EAAED,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzB;AACA,EAAE,IAAI,IAAI,KAAK,GAAG,IAAE,OAAO,OAAO,GAAC;AACnC;AACA,EAAE,IAAI,IAAI,KAAK,GAAG,EAAE;AACpB,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,IAAE,OAAO,OAAO,GAAC;AAC3D,GAAG,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,KAAK,SAAS,IAAE,OAAO,KAAK,GAAC;AAC9D,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,IAAE,OAAO,OAAO,GAAC;AACnE,GAAG;AACH;AACA,EAAE,IAAI,IAAI,KAAK,GAAG,IAAE,OAAO,UAAU,GAAC;AACtC;AACA,EAAEA,IAAM,OAAO,GAAG,OAAO,EAAE,CAAC;AAC5B;AACA,EAAEA,IAAM,OAAO,GAAG;AAClB,GAAG,IAAI,EAAE,SAAS;AAClB,YAAG,OAAO;AACV,GAAG,UAAU,EAAE,EAAE;AACjB,GAAG,QAAQ,EAAE,EAAE;AACf,GAAG,CAAC;AACJ;AACA,EAAE,IAAI,cAAc,EAAE;AACtB,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACzC,GAAG,MAAM;AACT,GAAG,IAAI,GAAG,OAAO,CAAC;AAClB,GAAG;AACH;AACA,EAAEC,IAAI,SAAS,CAAC;AAChB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,KAAK,SAAS,GAAG,YAAY,EAAE,CAAC,EAAE;AAC5D,GAAG,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC;AACxD,GAAG;AACH;AACA,EAAEA,IAAI,WAAW,GAAG,KAAK,CAAC;AAC1B;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,GAAG,CAAC,IAAI,CAAC,CAAC;AACV,GAAG,WAAW,GAAG,IAAI,CAAC;AACtB,GAAG;AACH;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,IAAI,CAAC,WAAW,EAAE;AACpB,GAAG,cAAc,GAAG,OAAO,CAAC;AAC5B,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF;AACA,CAAC,SAAS,OAAO,GAAG;AACpB,EAAED,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACzC,EAAE,IAAI,CAAC,CAAC,KAAK,IAAE,KAAK,CAAC,cAAc,CAAC,GAAC;AACrC;AACA,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF;AACA,CAAC,SAAS,KAAK,GAAG;AAClB,EAAEA,IAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACzC,EAAE,IAAI,CAAC,CAAC,KAAK,IAAE,KAAK,CAAC,cAAc,CAAC,GAAC;AACrC;AACA,EAAE,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC3D;AACA,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AAChB,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF;AACA,CAAC,SAAS,UAAU,GAAG;AACvB,EAAEA,IAAM,OAAO,GAAG,OAAO,EAAE,CAAC;AAC5B;AACA,EAAE,IAAI,CAAC,OAAO,IAAE,KAAK,CAAC,mBAAmB,CAAC,GAAC;AAC3C;AACA,EAAE,IAAI,OAAO,KAAK,cAAc,CAAC,OAAO,EAAE;AAC1C,GAAG,KAAK,8BAA2B,OAAO,iCAA2B,cAAc,CAAC,QAAO,QAAI,CAAC;AAChG,GAAG;AACH;AACA,EAAE,WAAW,EAAE,CAAC;AAChB;AACA,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,GAAG,KAAK,CAAC,YAAY,CAAC,CAAC;AACvB,GAAG;AACH;AACA,EAAE,KAAK,CAAC,GAAG,EAAE,CAAC;AACd,EAAE,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC3C;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,EAAE;AACF;AACA,CAAC,SAAS,OAAO,GAAG;AACpB,EAAEC,IAAI,IAAI,GAAG,EAAE,CAAC;AAChB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAE,IAAI,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,GAAC;AACvF;AACA,EAAE,OAAO,IAAI,CAAC;AACd,EAAE;AACF;AACA,CAAC,SAAS,YAAY,GAAG;AACzB,EAAE,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAE,OAAO,IAAI,GAAC;AAC/C,EAAE,WAAW,EAAE,CAAC;AAChB;AACA,EAAED,IAAM,IAAI,GAAG,OAAO,EAAE,CAAC;AACzB,EAAE,IAAI,CAAC,IAAI,IAAE,OAAO,IAAI,GAAC;AACzB;AACA,EAAEC,IAAI,KAAK,GAAG,IAAI,CAAC;AACnB;AACA,EAAE,WAAW,EAAE,CAAC;AAChB,EAAE,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;AACzB,GAAG,CAAC,IAAI,CAAC,CAAC;AACV,GAAG,WAAW,EAAE,CAAC;AACjB;AACA,GAAG,KAAK,GAAG,iBAAiB,EAAE,CAAC;AAC/B,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,IAAE,KAAK,GAAG,CAAC,KAAK,GAAC;AAC5D,GAAG;AACH;AACA,EAAE,OAAO,QAAE,IAAI,SAAE,KAAK,EAAE,CAAC;AACzB,EAAE;AACF;AACA,CAAC,SAAS,iBAAiB,GAAG;AAC9B,EAAE,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,uBAAuB,EAAE,GAAG,yBAAyB,EAAE,CAAC;AAC7F,EAAE;AACF;AACA,CAAC,SAAS,yBAAyB,GAAG;AACtC,EAAEA,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAE,GAAG;AACL,GAAGD,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1B,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,GAAG,EAAE;AACrD,IAAI,OAAO,KAAK,CAAC;AACjB,IAAI;AACJ;AACA,GAAG,KAAK,IAAI,IAAI,CAAC;AACjB,GAAG,CAAC,IAAI,CAAC,CAAC;AACV,GAAG,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;AAC9B;AACA,EAAE,OAAO,KAAK,CAAC;AACf,EAAE;AACF;AACA,CAAC,SAAS,uBAAuB,GAAG;AACpC,EAAEA,IAAM,SAAS,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAChC;AACA,EAAEC,IAAI,KAAK,GAAG,EAAE,CAAC;AACjB,EAAEA,IAAI,OAAO,GAAG,KAAK,CAAC;AACtB;AACA,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;AAC5B,GAAGD,IAAM,IAAI,GAAG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AAC5B,GAAG,IAAI,IAAI,KAAK,SAAS,IAAI,CAAC,OAAO,EAAE;AACvC,IAAI,OAAO,KAAK,CAAC;AACjB,IAAI;AACJ;AACA,GAAG,IAAI,IAAI,KAAK,IAAI,IAAI,CAAC,OAAO,EAAE;AAClC,IAAI,OAAO,GAAG,IAAI,CAAC;AACnB,IAAI;AACJ;AACA,GAAG,KAAK,IAAI,OAAO,WAAQ,QAAS,IAAI,CAAC;AACzC,GAAG,OAAO,GAAG,KAAK,CAAC;AACnB,GAAG;AACH,EAAE;AACF;AACA,CAAC,SAAS,WAAW,GAAG;AACxB,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAE,CAAC,IAAI,CAAC,GAAC;AACjE,EAAE;AACF;AACA,CAACC,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,CAAC;AACzB,CAAC,OAAO,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE;AAC3B,EAAE,IAAI,CAAC,KAAK,IAAE,KAAK,CAAC,sBAAsB,CAAC,GAAC;AAC5C,EAAE,KAAK,GAAG,KAAK,EAAE,CAAC;AAClB,EAAE,CAAC,IAAI,CAAC,CAAC;AACT,EAAE;AACF;AACA,CAAC,IAAI,KAAK,KAAK,OAAO,EAAE;AACxB,EAAE,KAAK,CAAC,yBAAyB,CAAC,CAAC;AACnC,EAAE;AACF;AACA,CAAC,IAAI,IAAI,CAAC,OAAO,KAAK,KAAK,IAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,GAAC;AACpD,CAAC,OAAO;AACR,EAAE,IAAI,EAAE,MAAM;AACd,EAAE,QAAQ,EAAE,CAAC,IAAI,CAAC;AAClB,EAAE,CAAC;AACH,CAAC;;;;"}