import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Dimensions,
  Animated,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface Agent {
  id: number;
  name: string;
  status: 'ACTIVE' | 'THINKING' | 'ERROR' | 'OFFLINE';
  task?: string;
  performance: number;
}

const { width, height } = Dimensions.get('window');

const AgentZeroControl: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([
    {
      id: 1,
      name: 'Agent 1',
      status: 'ACTIVE',
      task: 'Analyzing data',
      performance: 75
    },
    {
      id: 2,
      name: 'Agent 2',
      status: 'THINKING',
      performance: 60
    },
    {
      id: 3,
      name: 'Agent 3',
      status: 'ERROR',
      performance: 45
    },
    {
      id: 4,
      name: 'Agent 4',
      status: 'OFFLINE',
      performance: 0
    }
  ]);

  const [command, setCommand] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [pulseAnim] = useState(new Animated.Value(1));

  // Simuleer dynamische performance updates
  useEffect(() => {
    const interval = setInterval(() => {
      setAgents(prevAgents => 
        prevAgents.map(agent => {
          if (agent.status === 'ACTIVE') {
            const newPerformance = Math.min(100, agent.performance + Math.random() * 5 - 2);
            return { ...agent, performance: Math.max(0, newPerformance) };
          }
          return agent;
        })
      );
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  // Pulse animatie voor actieve agents
  useEffect(() => {
    const pulse = () => {
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]).start(() => pulse());
    };
    pulse();
  }, []);

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'ACTIVE':
        return {
          bg: '#FCD34D20',
          border: '#FCD34D',
          text: '#FCD34D',
          progress: '#F59E0B'
        };
      case 'THINKING':
        return {
          bg: '#60A5FA20',
          border: '#60A5FA',
          text: '#60A5FA',
          progress: '#3B82F6'
        };
      case 'ERROR':
        return {
          bg: '#F87171A0',
          border: '#F87171',
          text: '#F87171',
          progress: '#EF4444'
        };
      default:
        return {
          bg: '#6B728020',
          border: '#6B7280',
          text: '#6B7280',
          progress: '#6B7280'
        };
    }
  };

  const handleMicToggle = () => {
    setIsListening(!isListening);
  };

  const handleCommandSubmit = () => {
    if (command.trim()) {
      console.log('Command sent:', command);
      setCommand('');
    }
  };

  const renderAgent = (agent: Agent) => {
    const colors = getStatusColor(agent.status);
    
    return (
      <View key={agent.id} style={[styles.agentCard, { backgroundColor: colors.bg, borderColor: colors.border }]}>
        {/* Agent Header */}
        <View style={styles.agentHeader}>
          <Text style={[styles.agentName, { color: colors.text }]}>
            {agent.name}
          </Text>
          <View style={[styles.statusBadge, { backgroundColor: colors.bg, borderColor: colors.border }]}>
            <Text style={[styles.statusText, { color: colors.text }]}>
              {agent.status}
            </Text>
          </View>
        </View>

        {/* Task Info */}
        {agent.task && (
          <View style={styles.taskContainer}>
            <Text style={styles.taskLabel}>Task: </Text>
            <Text style={styles.taskText}>{agent.task}</Text>
          </View>
        )}

        {/* Performance Bar */}
        <View style={styles.performanceContainer}>
          <View style={styles.performanceHeader}>
            <Text style={styles.performanceLabel}>Performance</Text>
            <Text style={[styles.performanceValue, { color: colors.text }]}>
              {Math.round(agent.performance)}%
            </Text>
          </View>
          <View style={styles.progressBarContainer}>
            <View 
              style={[
                styles.progressBar, 
                { 
                  width: `${agent.performance}%`, 
                  backgroundColor: colors.progress 
                }
              ]} 
            />
          </View>
        </View>

        {/* Status Indicator */}
        <View style={styles.statusIndicator}>
          <View style={styles.statusLeft}>
            <Animated.View 
              style={[
                styles.statusDot, 
                { backgroundColor: colors.text },
                agent.status === 'ACTIVE' ? { transform: [{ scale: pulseAnim }] } : {}
              ]} 
            />
            <Text style={styles.statusMessage}>
              {agent.status === 'ACTIVE' ? 'Processing...' : 
               agent.status === 'THINKING' ? 'Analyzing...' :
               agent.status === 'ERROR' ? 'Error detected' : 'Offline'}
            </Text>
          </View>
          
          {agent.status === 'ACTIVE' && (
            <View style={styles.loadingDots}>
              {[...Array(3)].map((_, i) => (
                <Animated.View
                  key={i}
                  style={[
                    styles.loadingDot,
                    { backgroundColor: colors.progress },
                    { transform: [{ scale: pulseAnim }] }
                  ]}
                />
              ))}
            </View>
          )}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>AGENT ZERO CONTROL</Text>
        <View style={styles.headerLine} />
      </View>

      {/* Agent Cards */}
      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {agents.map(renderAgent)}
      </ScrollView>

      {/* Command Interface */}
      <View style={styles.commandInterface}>
        <View style={styles.commandRow}>
          {/* Voice Toggle */}
          <TouchableOpacity
            onPress={handleMicToggle}
            style={[
              styles.micButton,
              {
                backgroundColor: isListening ? '#EF444420' : '#06B6D420',
                borderColor: isListening ? '#EF4444' : '#06B6D4',
              }
            ]}
          >
            <Ionicons 
              name={isListening ? "mic-off" : "mic"} 
              size={20} 
              color={isListening ? '#EF4444' : '#06B6D4'} 
            />
          </TouchableOpacity>

          {/* Command Input */}
          <View style={styles.inputContainer}>
            <TextInput
              value={command}
              onChangeText={setCommand}
              placeholder="Enter command for agents..."
              placeholderTextColor="#6B7280"
              style={styles.textInput}
            />
            {isListening && (
              <View style={styles.listeningIndicator}>
                {[...Array(3)].map((_, i) => (
                  <Animated.View
                    key={i}
                    style={[
                      styles.listeningDot,
                      { transform: [{ scale: pulseAnim }] }
                    ]}
                  />
                ))}
              </View>
            )}
          </View>

          {/* Send Button */}
          <TouchableOpacity
            onPress={handleCommandSubmit}
            disabled={!command.trim()}
            style={[
              styles.sendButton,
              { opacity: command.trim() ? 1 : 0.5 }
            ]}
          >
            <Text style={styles.sendButtonText}>SEND</Text>
          </TouchableOpacity>
        </View>

        {/* Status Bar */}
        <View style={styles.statusBar}>
          <View style={styles.statusItem}>
            <View style={[styles.statusBarDot, { backgroundColor: '#FCD34D' }]} />
            <Text style={styles.statusBarText}>Active: {agents.filter(a => a.status === 'ACTIVE').length}</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusBarDot, { backgroundColor: '#60A5FA' }]} />
            <Text style={styles.statusBarText}>Thinking: {agents.filter(a => a.status === 'THINKING').length}</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusBarDot, { backgroundColor: '#F87171' }]} />
            <Text style={styles.statusBarText}>Errors: {agents.filter(a => a.status === 'ERROR').length}</Text>
          </View>
          <View style={styles.statusItem}>
            <View style={[styles.statusBarDot, { backgroundColor: '#6B7280' }]} />
            <Text style={styles.statusBarText}>Offline: {agents.filter(a => a.status === 'OFFLINE').length}</Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000000',
  },
  header: {
    paddingTop: 60,
    paddingBottom: 20,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    letterSpacing: 2,
    fontFamily: 'Courier New',
  },
  headerLine: {
    height: 1,
    width: '100%',
    backgroundColor: '#06B6D4',
    marginTop: 15,
    opacity: 0.7,
  },
  scrollContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  agentCard: {
    marginBottom: 16,
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
  },
  agentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  agentName: {
    fontSize: 18,
    fontWeight: 'bold',
    fontFamily: 'Courier New',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 20,
    borderWidth: 1,
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
    letterSpacing: 1,
    fontFamily: 'Courier New',
  },
  taskContainer: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  taskLabel: {
    color: '#06B6D4',
    fontSize: 14,
    fontFamily: 'Courier New',
  },
  taskText: {
    color: '#D1D5DB',
    fontSize: 14,
    fontFamily: 'Courier New',
  },
  performanceContainer: {
    marginBottom: 8,
  },
  performanceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  performanceLabel: {
    color: '#9CA3AF',
    fontSize: 12,
    fontFamily: 'Courier New',
  },
  performanceValue: {
    fontSize: 12,
    fontWeight: '600',
    fontFamily: 'Courier New',
  },
  progressBarContainer: {
    height: 8,
    backgroundColor: '#374151',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 4,
  },
  statusIndicator: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusMessage: {
    color: '#9CA3AF',
    fontSize: 12,
    fontFamily: 'Courier New',
  },
  loadingDots: {
    flexDirection: 'row',
  },
  loadingDot: {
    width: 4,
    height: 16,
    borderRadius: 2,
    marginHorizontal: 1,
  },
  commandInterface: {
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
    borderTopWidth: 1,
    borderTopColor: 'rgba(6, 182, 212, 0.3)',
    paddingHorizontal: 16,
    paddingVertical: 16,
    paddingBottom: 40,
  },
  commandRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  micButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  inputContainer: {
    flex: 1,
    position: 'relative',
    marginRight: 12,
  },
  textInput: {
    backgroundColor: 'rgba(55, 65, 81, 0.5)',
    borderWidth: 2,
    borderColor: '#6B7280',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    color: '#FFFFFF',
    fontSize: 16,
    fontFamily: 'Courier New',
  },
  listeningIndicator: {
    position: 'absolute',
    right: 12,
    top: '50%',
    transform: [{ translateY: -8 }],
    flexDirection: 'row',
  },
  listeningDot: {
    width: 4,
    height: 16,
    backgroundColor: '#EF4444',
    borderRadius: 2,
    marginHorizontal: 1,
  },
  sendButton: {
    backgroundColor: '#0891B2',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#06B6D4',
  },
  sendButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
    fontFamily: 'Courier New',
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  statusItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 4,
  },
  statusBarDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 4,
  },
  statusBarText: {
    color: '#9CA3AF',
    fontSize: 10,
    fontFamily: 'Courier New',
  },
});

export default AgentZeroControl;
