"""
You can run either this notebook locally (if you have all the dependencies and a GPU) or on Google Colab.

Instructions for setting up Colab are as follows:
1. Open a new Python 3 notebook.
2. Import this notebook from GitHub (File -> Upload Notebook -> "GitHub" tab -> copy/paste GitHub URL)
3. Connect to an instance with a GPU (Runtime -> Change runtime type -> select "GPU" for hardware accelerator)
4. Run this cell to set up dependencies.
5. Restart the runtime (Runtime -> Restart Runtime) for any upgraded packages to take effect


NOTE: User is responsible for checking the content of datasets and the applicable licenses and determining if suitable for the intended use.
"""
# If you're using Google Colab and not running locally, run this cell.
import os

# Install dependencies
!pip install wget
!apt-get install sox libsndfile1 ffmpeg
!pip install text-unidecode
!pip install matplotlib>=3.3.2

## Install NeMo
BRANCH = 'main'
!python -m pip install "nemo_toolkit[asr] @ git+https://github.com/NVIDIA/NeMo.git@$BRANCH"

import os
import json

import nemo.collections.asr as nemo_asr

model = nemo_asr.models.ASRModel.from_pretrained("nvidia/canary-1b")

model.replace_adapter_compatible_modules()

model.adapter_module_names

from nemo.collections.common.parts import LinearAdapterConfig

input_dim = model.cfg.encoder.d_model
adapter_dim = 8

enc_adapter_cfg = LinearAdapterConfig(in_features=input_dim, dim=adapter_dim)
dec_adapter_cfg = LinearAdapterConfig(in_features=input_dim, dim=adapter_dim)

model.add_adapter(name="encoder:enc", cfg=enc_adapter_cfg)
model.add_adapter(name="transf_decoder:dec", cfg=dec_adapter_cfg)

print("Added adapters!")

model.freeze()
model.unfreeze_enabled_adapters()

model.summarize()

model.get_enabled_adapters()

model.prompt_format

from nemo.collections.common.data.prompt_fn import get_prompt_format_fn, registered_prompt_format_fn
from nemo.collections.common.prompts import CanaryPromptFormatter, PromptFormatter

# sample audio data
import numpy as np
import soundfile as sf
from io import BytesIO
from lhotse import Recording, SupervisionSegment, CutSet

def create_sine_wave(duration: float = 1.0, sample_rate: int = 16000, frequency: float = 440.0):
    """Generate a sine wave of specified duration and frequency."""
    t = np.linspace(0, duration, int(duration * sample_rate))
    return np.sin(2 * np.pi * frequency * t)

audio = create_sine_wave()
    
    # Convert to 16-bit PCM WAV format in memory
buffer = BytesIO()
sf.write(buffer, audio, 16000, format='WAV')
audio_bytes = buffer.getvalue()

# Create a Recording from the bytes
cut = Recording.from_bytes(
    data=audio_bytes,
    recording_id="generated_sine"
).to_cut()

cut.supervisions = [SupervisionSegment(cut.id, cut.recording.id, start=0, duration=cut.duration, text="I said something")]

canary_prompt_format_fn = get_prompt_format_fn(cut, CanaryPromptFormatter)
canary_prompt_format_fn?

from nemo.collections.common.prompts import PromptFormatter
from lhotse.cut import Cut
@registered_prompt_format_fn(Cut, PromptFormatter)
def canary_custom(example, formatter):
    """ Users can implement this as needed """
    raise NotImplementedError()

print("Registered prompt")

temp = get_prompt_format_fn(Cut, PromptFormatter)
temp.__name__

# Let's review the actual prompt formatter clas docs
model.prompt?

# Let's see the actual template of this prompt formatter
model.prompt.TEMPLATE

# Create a new prompt formatter using the original CanaryPromptFormatter class as baseclass
class CanaryPromptFormatterV2(model.prompt.__class__):

    # make sure to provide a new name
    NAME: str = "canary_custom"

    # Make any changes as necessary.
    # For this demonstration, we will not change anything other than the name

# Next, lets update the model's prompt formatter
model.change_prompt("canary_custom")

# Check if everything is ok -
model.prompt.__class__.__name__

model.prompt_format

model.change_prompt('canary')

from nemo.collections.asr.data.audio_to_text_lhotse_prompted import PromptedAudioToTextLhotseDataset

# Uncomment below line to see the class definition of PromptedAudioToTextLhotseDataset
# PromptedAudioToTextLhotseDataset??

import torch.utils.data
from lhotse import CutSet
from lhotse.cut import MixedCut, MonoCut
from lhotse.dataset import AudioSamples
from lhotse.dataset.collation import collate_vectors

from nemo.collections.asr.data.audio_to_text_lhotse_prompted import PromptedAudioToTextLhotseDataset, PromptedAudioToTextMiniBatch

class MyCanaryPromptedAudioToTextLhotseDataset(torch.utils.data.Dataset):
    """
    This dataset is based on :class:`~nemo.collections.asr.data.audio_to_text_lhotse.LhotseSpeechToTextBpeDataset`.
    It is a Lhotse-style dataset that converts a mini-batch of Cuts into tensors.
    The main difference from ``LhotseSpeechToTextBpeDataset`` is that we introduce
    a special prompt format for multitask encoder-decoder models.

    To perform the prompt formatting, we accept a ``prompt_format_fn``.
    It's expected to accept:
    * a ``Cut`` a single MonoCut or MixedCut
    * a ``PromptFormatter`` Prepend and append control tokens to the token sequence

    Tokenized utterances will be extended with special prompt tokens according to ``prompt_format_fn`` logic.
    We support cuts with multiple supervision segments -- their tokenized texts will be concatenated before we add the prompt tokens.
    This is useful, for example, in code-switched scenarios where each segment is spoken in a different language.
    """

    def __init__(
        self,
        tokenizer: 'TokenizerSpec',
        prompt: PromptFormatter
    ):
        super().__init__()
        self.tokenizer = tokenizer
        self.load_audio = AudioSamples(fault_tolerant=True)
        self.padding_value = self.tokenizer.pad_id
        self.prompt = prompt
        self.prompt_format_fn = get_prompt_format_fn(Cut, self.prompt)  # Use the default canary prompt function


    def __getitem__(self, cuts: CutSet) -> PromptedAudioToTextMiniBatch:
        audio, audio_lens, cuts = self.load_audio(cuts)
        answers = []
        prompts = []
        prompts_with_answers = []

        for cut in cuts:
            prompted_answers = self.prompt_format_fn(cut, self.prompt)
            answers.append(prompted_answers["answer_ids"])
            prompts.append(prompted_answers["context_ids"])
            prompts_with_answers.append(prompted_answers["input_ids"])
        
        transcript, transcript_lens = self._collate_tokens(answers)
        prompts_with_answers, prompts_with_answers_lens = self._collate_tokens(prompts_with_answers)
        prompts, prompt_lens = self._collate_tokens(prompts)

        return PromptedAudioToTextMiniBatch(
            audio=audio,
            audio_lens=audio_lens,
            transcript=transcript,
            transcript_lens=transcript_lens,
            prompt=prompts,
            prompt_lens=prompt_lens,
            prompted_transcript=prompts_with_answers,
            prompted_transcript_lens=prompts_with_answers_lens,
            cuts=cuts.drop_in_memory_data(),
        )

    def _collate_tokens(self, tokens: list[list[int] | torch.Tensor]) -> tuple[torch.Tensor, torch.Tensor]:
        tokens = [torch.as_tensor(t) for t in tokens]
        token_lens = torch.tensor([t.size(0) for t in tokens], dtype=torch.long)
        tokens = collate_vectors(tokens, padding_value=self.padding_value)
        return tokens, token_lens


model.prompt.get_roles()

for role in model.prompt.get_roles():
    print(role, model.prompt.get_slots(role))
    print()

import os
import glob
import json
import copy
import subprocess
import tarfile
import wget
import librosa
import tqdm
from omegaconf import OmegaConf

from torch.utils.data import DataLoader, Dataset

import lightning.pytorch as L

from transformers import T5Tokenizer, T5ForConditionalGeneration

from nemo.collections.asr.parts.utils.manifest_utils import read_manifest, write_manifest
from nemo.collections.common.data.lhotse import get_lhotse_dataloader_from_config


# Function to build a manifest
def build_manifest(transcripts_path, manifest_path, wav_path, data_dir):
    with open(transcripts_path, 'r') as fin:
        with open(manifest_path, 'w') as fout:
            for line in fin:
                # Lines look like this:
                # <s> transcript </s> (fileID)
                transcript = line[: line.find('(')-1].lower()
                transcript = transcript.replace('<s>', '').replace('</s>', '')
                transcript = transcript.strip()

                file_id = line[line.find('(')+1 : -2]  # e.g. "cen4-fash-b"
                audio_path = os.path.join(
                    data_dir, wav_path,
                    file_id[file_id.find('-')+1 : file_id.rfind('-')],
                    file_id + '.wav')

                duration = librosa.core.get_duration(path=audio_path)

                # Write the metadata to the manifest
                metadata = {
                    "audio_filepath": audio_path,
                    "duration": duration,
                    "text": transcript,
                    "pnc": "no",
                    "source_lang": "en",
                    "target_lang": "en",
                    "task": "asr",
                }
                json.dump(metadata, fout)
                fout.write('\n')

    return manifest_path


class CanaryAN4DataModule(L.LightningDataModule):

    def __init__(self, tokenizer, data_dir: str = "./an4/", batch_size=8):
        super().__init__()
        self.tokenizer = tokenizer
        self.data_dir = data_dir
        self.batch_size = batch_size

        # ASR manifests
        self.train_manifest = data_dir + '/an4/train_manifest.json'
        self.test_manifest = data_dir + '/an4/test_manifest.json'

        # AST manifests
        self.ast_train_manifest = data_dir + '/an4/ast_train_manifest.json'
        self.ast_test_manifest = data_dir + '/an4/ast_test_manifest.json'

        # Combined manifests
        self.combined_train_manifest = data_dir + '/an4/combined_train_manifest.json'
        self.combined_test_manifest = data_dir + '/an4/combined_test_manifest.json'

    def setup(self, stage):
        # make assignments here (val/train/test split)
        # called on every process in DDP
        # Assign train/val datasets for use in dataloaders
        pass

    def train_dataloader(self):
        config = {'manifest_filepath': self.combined_train_manifest, 'batch_size': self.batch_size,
                  'num_workers': 4, 'shuffle': True, 'min_duration': 0.3, 'max_duration': 10.0}
        return self._setup_dataloader(config)

    def val_dataloader(self):
        config = {'manifest_filepath': self.combined_test_manifest, 'batch_size': self.batch_size,
                  'num_workers': 4, 'shuffle': False, 'min_duration': 0.3, 'max_duration': 10.0}
        return self._setup_dataloader(config)

    def test_dataloader(self):
        config = {'manifest_filepath': self.combined_test_manifest, 'batch_size': self.batch_size,
                  'num_workers': 4, 'shuffle': False, 'min_duration': 0.3, 'max_duration': 10.0}
        return self._setup_dataloader(config)

    def teardown(self, stage):
        # clean up after fit or test
        # called on every process in DDP
        pass

    def _setup_dataloader(self, config):
        """
        The main function that creates the data loader using Lhotse's integration with NeMo.
        """
        return get_lhotse_dataloader_from_config(
                OmegaConf.create(config),
                global_rank=self.trainer.global_rank,
                world_size=self.trainer.world_size,
                # Note the passing of our custom dataset
                dataset=MyCanaryPromptedAudioToTextLhotseDataset(tokenizer=self.tokenizer, prompt=CanaryPromptFormatter(self.tokenizer)),
            )

    def prepare_data(self):
        # download, split, etc...
        # only called on 1 GPU/TPU in distributed
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir)

        data_dir = self.data_dir
        if not os.path.exists(data_dir + '/an4_sphere.tar.gz'):
            an4_url = 'https://dldata-public.s3.us-east-2.amazonaws.com/an4_sphere.tar.gz'
            an4_path = wget.download(an4_url, data_dir)
            print(f"Dataset downloaded at: {an4_path}")
        else:
            print("Tarfile already exists.")
            an4_path = data_dir + '/an4_sphere.tar.gz'

        if not os.path.exists(data_dir + '/an4/'):
            # Untar and convert .sph to .wav (using sox)
            tar = tarfile.open(an4_path)
            tar.extractall(path=data_dir)

            print("Converting .sph to .wav...")
            sph_list = glob.glob(data_dir + '/an4/**/*.sph', recursive=True)
            for sph_path in sph_list:
                wav_path = sph_path[:-4] + '.wav'
                cmd = ["sox", sph_path, wav_path]
                subprocess.run(cmd)
        print("Finished conversion.\n******")

        # Building Manifests
        print("******")
        train_transcripts = data_dir + '/an4/etc/an4_train.transcription'
        train_manifest = self.train_manifest
        if not os.path.isfile(train_manifest):
            build_manifest(train_transcripts, train_manifest, 'an4/wav/an4_clstk', data_dir)
            print("Training manifest created.")

        test_transcripts = data_dir + '/an4/etc/an4_test.transcription'
        test_manifest = self.test_manifest
        if not os.path.isfile(test_manifest):
            build_manifest(test_transcripts, test_manifest, 'an4/wav/an4test_clstk', data_dir)
            print("Test manifest created.")
        print("*** Wrote manifests for Eng ***")

        train_manifest_data = read_manifest(self.train_manifest)
        test_manifest_data = read_manifest(self.test_manifest)

        if not os.path.isfile(self.ast_train_manifest) or not os.path.isfile(self.ast_test_manifest) or not os.path.isfile(self.combined_train_manifest) or not os.path.isfile(self.combined_test_manifest):
            tokenizer = T5Tokenizer.from_pretrained("google-t5/t5-small")
            t5_model = T5ForConditionalGeneration.from_pretrained("google-t5/t5-small")

            if torch.cuda.is_available():
                t5_model = t5_model.cuda()

            def pipe(text):
                if isinstance(text, str):
                    text = [text]

                prefix = "translate English to German"
                prompts = [prefix + ": " + x for x in text]
                input_ids = tokenizer(prompts, return_tensors="pt", padding=True, truncation=True).input_ids
                input_ids = input_ids.to(t5_model.device)
                outputs = t5_model.generate(input_ids, max_new_tokens=64)
                return [tokenizer.decode(output, skip_special_tokens=True) for output in outputs]

            ast_train_manifest_data = copy.deepcopy(train_manifest_data)
            ast_test_manifest_data = copy.deepcopy(test_manifest_data)

            print("Translating train set")
            train_texts = [x['text'] for x in train_manifest_data]
            BATCH_SIZE = 32

            for i in tqdm.tqdm(range(0, len(train_texts), BATCH_SIZE), total=len(train_texts) // BATCH_SIZE):
                batch_texts = train_texts[i:i+BATCH_SIZE]
                batch_texts = pipe(batch_texts)
                for j, text in enumerate(batch_texts):
                    ast_train_manifest_data[i+j]['text'] = text
                    ast_train_manifest_data[i+j]['task'] = 'ast'
                    ast_train_manifest_data[i+j]['target_lang'] = 'de'

            print("Translating test set")
            for data in tqdm.tqdm(ast_test_manifest_data, total=len(ast_test_manifest_data)):
                data['text'] = pipe(data['text'])[0]
                data['task'] = 'ast'
                data['target_lang'] = 'de'

            write_manifest(self.ast_train_manifest, ast_train_manifest_data)
            write_manifest(self.ast_test_manifest, ast_test_manifest_data)

            print("*** Wrote ast manifests ***")

            combined_train, combined_test = [], []
            combined_train.extend(train_manifest_data)
            combined_train.extend(ast_train_manifest_data)

            combined_test.extend(test_manifest_data)
            combined_test.extend(ast_test_manifest_data)

            write_manifest(self.combined_train_manifest, combined_train)
            write_manifest(self.combined_test_manifest, combined_test)
            print("*** Wrote combined manifests ***")

        else:
            print("*** Wrote ast and combined manifests ***")


data_module = CanaryAN4DataModule(tokenizer=model.tokenizer, batch_size=16)

data_module.prepare_data()

!head -n 5 {data_module.train_manifest}

!head -n 5 {data_module.ast_train_manifest}

from nemo.collections.asr.metrics.wer import word_error_rate
from torchmetrics.text import SacreBLEUScore

asr_test = read_manifest(data_module.test_manifest)
ast_test = read_manifest(data_module.ast_test_manifest)

asr_filepaths = [x['audio_filepath'] for x in asr_test]
asr_gt = [x['text'] for x in asr_test]

ast_filepaths = [x['audio_filepath'] for x in ast_test]
ast_gt = [x['text'] for x in ast_test]

print("Num files:", len(asr_filepaths))

if torch.cuda.is_available():
    model = model.cuda()  # move model to gpu
    model = model.to(torch.bfloat16)  # cast full model to bfloat16

asr_preds = model.transcribe(asr_filepaths, pnc='no', task='asr', source_lang='en', target_lang='en', batch_size=32)

ast_preds = model.transcribe(ast_filepaths, pnc='no', task='ast', source_lang='en', target_lang='de', batch_size=32)

wer = word_error_rate([p.text for p in asr_preds], asr_gt)
print("WER", wer)

sacrebleu = SacreBLEUScore(n_gram=4)
scores = []
preds = []
gts = []
for pred, gt in zip(ast_preds, ast_gt):
    preds.append(pred)
    gts.append([gt])

# bleu = sum(scores) / len(scores)
sacrebleu.update([p.text for p in preds], gts)
bleu = sacrebleu.compute()
print("BLEU", bleu.item() * 100)

print(OmegaConf.to_yaml(model.cfg.optim))

# Setup optimization
model.cfg.optim.lr = 3e-4
model.cfg.optim.sched.warmup_steps = 25

from omegaconf import OmegaConf
from nemo.utils import exp_manager

trainer = L.Trainer(max_steps=200, accumulate_grad_batches=1, logger=False, enable_checkpointing=False, check_val_every_n_epoch=5)

# # Environment variable generally used for multi-node multi-gpu training.
# # In notebook environments, this flag is unnecessary and can cause logs of multiple training runs to overwrite each other.
# os.environ.pop('NEMO_EXPM_VERSION', None)

# config = exp_manager.ExpManagerConfig(
#     exp_dir=f'experiments/canary/',
#     name=f"Canary-Model-Adapter-Training",
#     checkpoint_callback_params=exp_manager.CallbackParams(
#         monitor="val_wer",
#         mode="min",
#         always_save_nemo=False,
#         save_best_model=False,
#     ),
# )

# config = OmegaConf.structured(config)

# logdir = exp_manager.exp_manager(trainer, config)

trainer.fit(model, data_module)

model.save_adapters("adapters.pt")
!ls -l -- *.pt
!du -sh *.pt

asr_test = read_manifest(data_module.test_manifest)
ast_test = read_manifest(data_module.ast_test_manifest)

asr_filepaths = [x['audio_filepath'] for x in asr_test]
asr_gt = [x['text'] for x in asr_test]

ast_filepaths = [x['audio_filepath'] for x in ast_test]
ast_gt = [x['text'] for x in ast_test]

print("Num files:", len(asr_filepaths))

if torch.cuda.is_available():
    model = model.cuda()
    model = model.to(torch.bfloat16)

asr_preds = model.transcribe(asr_filepaths, pnc='no', task='asr', source_lang='en', target_lang='en', batch_size=32)

ast_preds = model.transcribe(ast_filepaths, pnc='no', task='ast', source_lang='en', target_lang='de', batch_size=32)

from nemo.collections.asr.metrics.wer import word_error_rate
from torchmetrics.text import SacreBLEUScore

wer = word_error_rate([p.text for p in asr_preds], asr_gt)
print("WER", wer)

sacrebleu = SacreBLEUScore(n_gram=4)
scores = []
preds = []
gts = []
for pred, gt in zip(ast_preds, ast_gt):
    preds.append(pred)
    gts.append([gt])

# bleu = sum(scores) / len(scores)
sacrebleu.update([p.text for p in preds], gts)
bleu = sacrebleu.compute()
print("BLEU", bleu.item() * 100)